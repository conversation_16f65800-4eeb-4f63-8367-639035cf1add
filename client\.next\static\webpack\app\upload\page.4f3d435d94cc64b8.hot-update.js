"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/page.tsx":
/*!*****************************!*\
  !*** ./app/upload/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UploadPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ESPECIALIDADES = [\n    {\n        value: 'ginecologia-obstetricia',\n        label: 'Ginecología y Obstetricia'\n    },\n    {\n        value: 'medicina-interna',\n        label: 'Medicina Interna'\n    },\n    {\n        value: 'cirugia',\n        label: 'Cirugía'\n    },\n    {\n        value: 'pediatria',\n        label: 'Pediatría'\n    },\n    {\n        value: 'salud-publica',\n        label: 'Salud Pública'\n    }\n];\nconst TEMAS_GINECOBS = [\n    {\n        value: '1-control-prenatal',\n        label: 'Control Prenatal'\n    },\n    {\n        value: '2-embarazo-multiple',\n        label: 'Embarazo Múltiple'\n    },\n    {\n        value: '3-hemorragias-primera-mitad',\n        label: 'Hemorragias de la Primera Mitad del Embarazo'\n    },\n    {\n        value: '4-hemorragias-segunda-mitad',\n        label: 'Hemorragias de la Segunda Mitad del Embarazo'\n    },\n    {\n        value: '5-rotura-prematura-membranas',\n        label: 'Síndrome de Rotura Prematura de Membranas'\n    },\n    {\n        value: '6-parto-prematuro',\n        label: 'Parto Prematuro'\n    },\n    {\n        value: '7-embarazo-prolongado',\n        label: 'Embarazo Prolongado'\n    },\n    {\n        value: '8-hipertension-embarazo',\n        label: 'Estados Hipertensivos del Embarazo'\n    },\n    {\n        value: '9-diabetes-gestacional',\n        label: 'Diabetes Gestacional'\n    },\n    {\n        value: '10-trabajo-parto',\n        label: 'Trabajo de Parto'\n    },\n    {\n        value: '11-mecanismo-parto',\n        label: 'Mecanismo del Parto en General'\n    },\n    {\n        value: '12-atencion-parto',\n        label: 'Atención del Parto'\n    },\n    {\n        value: '13-lactancia-materna',\n        label: 'Lactancia Materna'\n    },\n    {\n        value: '14-puerperio-normal',\n        label: 'Puerperio Normal'\n    },\n    {\n        value: '15-puerperio-patologico',\n        label: 'Puerperio Patológico'\n    },\n    {\n        value: '16-its',\n        label: 'Infecciones de Transmisión Sexual'\n    },\n    {\n        value: '17-patologia-endometrio',\n        label: 'Patología del Endometrio'\n    },\n    {\n        value: '18-amenorrea',\n        label: 'Amenorrea'\n    },\n    {\n        value: '19-patologia-cervicouterina',\n        label: 'Patología Cervicouterina'\n    },\n    {\n        value: '20-cancer-mama',\n        label: 'Cáncer de Mama'\n    },\n    {\n        value: '21-cancer-ovario',\n        label: 'Cáncer de Ovario'\n    },\n    {\n        value: '22-climaterio-menopausia',\n        label: 'Climaterio y Menopausia'\n    },\n    {\n        value: '23-endometriosis',\n        label: 'Endometriosis'\n    },\n    {\n        value: '24-epi',\n        label: 'Enfermedad Pélvica Inflamatoria'\n    },\n    {\n        value: '25-infecciones-vaginales',\n        label: 'Infecciones Vaginales'\n    },\n    {\n        value: '26-miomatosis-uterina',\n        label: 'Miomatosis Uterina'\n    },\n    {\n        value: '27-patologia-benigna-mama',\n        label: 'Patología Benigna de Mama'\n    },\n    {\n        value: '28-patologia-benigna-ovario',\n        label: 'Patología Benigna de Ovario'\n    },\n    {\n        value: '29-salud-sexual',\n        label: 'Salud Sexual y Reproductiva'\n    },\n    {\n        value: '30-aborto',\n        label: 'Aborto'\n    },\n    {\n        value: '31-metrorragias',\n        label: 'Metrorragias'\n    },\n    {\n        value: '32-sop',\n        label: 'Síndrome de Ovario Poliquístico'\n    }\n];\nfunction UploadPage() {\n    var _ESPECIALIDADES_find, _TEMAS_GINECOBS_find, _ESPECIALIDADES_find1;\n    _s();\n    const [especialidad, setEspecialidad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tema, setTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [video, setVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideo, setSelectedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch videos when component mounts\n    const fetchVideos = async ()=>{\n        try {\n            const response = await fetch('/api/v1/upload-content/videos');\n            if (!response.ok) throw new Error('Error al cargar los videos');\n            const data = await response.json();\n            setVideos(data);\n        } catch (err) {\n            console.error('Error fetching videos:', err);\n            setError('Error al cargar los videos');\n        }\n    };\n    // Load videos on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadPage.useEffect\": ()=>{\n            fetchVideos();\n        }\n    }[\"UploadPage.useEffect\"], []);\n    const handleDelete = async (videoId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar este video?')) return;\n        try {\n            const response = await fetch(\"/api/v1/upload-content/videos/\".concat(videoId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar el video');\n            // Remove video from state\n            setVideos(videos.filter((v)=>v.id !== videoId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (err) {\n            console.error('Error deleting video:', err);\n            setError('Error al eliminar el video');\n        }\n    };\n    const handleEdit = (video)=>{\n        setSelectedVideo(video);\n        setIsEditing(true);\n        setEspecialidad(video.especialidad);\n        setTema(video.tema);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!especialidad || !tema || !isEditing && !video) {\n            setError('Por favor complete todos los campos');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            const formData = new FormData();\n            if (video) formData.append('file', video);\n            formData.append('especialidad', especialidad);\n            formData.append('tema', tema);\n            formData.append('titulo', video ? video.name : selectedVideo.titulo);\n            const url = isEditing ? \"/api/v1/upload-content/videos/\".concat(selectedVideo.id) : '/api/v1/upload-content/upload-video';\n            const response = await fetch(url, {\n                method: isEditing ? 'PUT' : 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                fetchVideos(); // Refresh the video list\n                if (!isEditing) {\n                    setVideo(null);\n                    setTema('');\n                    setEspecialidad('');\n                }\n            } else {\n                throw new Error(result.message || \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n            }\n        } catch (err) {\n            console.error('Error:', err);\n            setError(err instanceof Error ? err.message : \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-4xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Gestionar Lecciones\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-8 md:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: isEditing ? 'Editar Lección' : 'Subir Nueva Lección'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: \"Especialidad M\\xe9dica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full justify-between\",\n                                                            children: [\n                                                                especialidad ? (_ESPECIALIDADES_find = ESPECIALIDADES.find((e)=>e.value === especialidad)) === null || _ESPECIALIDADES_find === void 0 ? void 0 : _ESPECIALIDADES_find.label : 'Seleccione una especialidad',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"▼\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                        className: \"w-[300px]\",\n                                                        children: ESPECIALIDADES.map((esp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onSelect: ()=>{\n                                                                    setEspecialidad(esp.value);\n                                                                    setTema(''); // Reset tema when especialidad changes\n                                                                },\n                                                                className: \"cursor-pointer p-2 hover:bg-gray-100\",\n                                                                children: esp.label\n                                                            }, esp.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 17\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 9\n                                    }, this),\n                                    especialidad === 'ginecologia-obstetricia' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: \"Tema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full justify-between\",\n                                                            children: [\n                                                                tema ? (_TEMAS_GINECOBS_find = TEMAS_GINECOBS.find((t)=>t.value === tema)) === null || _TEMAS_GINECOBS_find === void 0 ? void 0 : _TEMAS_GINECOBS_find.label : 'Seleccione un tema',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"▼\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                        className: \"w-[300px]\",\n                                                        children: TEMAS_GINECOBS.map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onSelect: ()=>setTema(t.value),\n                                                                className: \"cursor-pointer p-2 hover:bg-gray-100\",\n                                                                children: t.label\n                                                            }, t.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 11\n                                    }, this),\n                                    especialidad && ![\n                                        'ginecologia-obstetricia'\n                                    ].includes(especialidad) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"tema\",\n                                                children: \"Tema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"tema\",\n                                                placeholder: \"Ingrese el tema de \".concat((_ESPECIALIDADES_find1 = ESPECIALIDADES.find((e)=>e.value === especialidad)) === null || _ESPECIALIDADES_find1 === void 0 ? void 0 : _ESPECIALIDADES_find1.label),\n                                                value: tema,\n                                                onChange: (e)=>setTema(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"video\",\n                                                children: \"Video de la lecci\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"video\",\n                                                type: \"file\",\n                                                accept: \"video/*\",\n                                                onChange: (e)=>{\n                                                    var _e_target_files;\n                                                    return setVideo(((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 11\n                                            }, this),\n                                            video && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Archivo seleccionado: \",\n                                                    video.name,\n                                                    \" (\",\n                                                    (video.size / (1024 * 1024)).toFixed(2),\n                                                    \" MB)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 9\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-red-100 border border-red-400 text-red-700 rounded\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 11\n                                    }, this),\n                                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-green-100 border border-green-400 text-green-700 rounded\",\n                                        children: \"\\xa1Lecci\\xf3n subida exitosamente!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"submit\",\n                                            disabled: isUploading,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                            children: isUploading ? 'Subiendo...' : 'Subir Lección'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 11\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Lecciones Subidas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: videos.map((video)=>{\n                                    var _ESPECIALIDADES_find;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border p-4 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: video.titulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    (_ESPECIALIDADES_find = ESPECIALIDADES.find((e)=>e.value === video.especialidad)) === null || _ESPECIALIDADES_find === void 0 ? void 0 : _ESPECIALIDADES_find.label,\n                                                    \" - \",\n                                                    video.tema\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleEdit(video),\n                                                        children: \"Editar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"destructive\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleDelete(video.id),\n                                                        children: \"Eliminar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, video.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadPage, \"v7+DHRNQ+pKYgpYtd7PsCDeYjK0=\");\n_c = UploadPage;\nvar _c;\n$RefreshReg$(_c, \"UploadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/page.tsx\n"));

/***/ })

});