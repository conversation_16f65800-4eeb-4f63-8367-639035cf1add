from __future__ import annotations

from datetime import datetime
from typing import Optional

from sqlalchemy import DateTime, ForeignKey, Integer, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..db.models import Base


class Lecciones(Base):
    __tablename__ = "lecciones"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    slug: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)

    ginecologia_obstetricias: Mapped[list[GinecologiaObstetricia]] = relationship(
        back_populates="lecciones", cascade="all, delete-orphan"
    )


class GinecologiaObstetricia(Base):
    __tablename__ = "ginecologia_obstetricia"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    lecciones_id: Mapped[int] = mapped_column(ForeignKey("lecciones.id", ondelete="CASCADE"), nullable=False)
    slug: Mapped[str] = mapped_column(String(255), nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)

    lecciones: Mapped[Lecciones] = relationship(back_populates="ginecologia_obstetricias")
    lecciones_items: Mapped[list[Leccion]] = relationship(back_populates="ginecologia_obstetricia", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint("lecciones_id", "slug", name="uq_ginobs_lecciones_slug"),
    )


class Leccion(Base):
    __tablename__ = "leccion"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    ginecologia_obstetricia_id: Mapped[int] = mapped_column(ForeignKey("ginecologia_obstetricia.id", ondelete="CASCADE"), nullable=False)
    slug: Mapped[str] = mapped_column(String(255), nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    # Optional: link to uploaded video content
    video_id: Mapped[Optional[int]] = mapped_column(ForeignKey("content_videos.id", ondelete="SET NULL"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)

    ginecologia_obstetricia: Mapped[GinecologiaObstetricia] = relationship(back_populates="lecciones_items")

    __table_args__ = (
        UniqueConstraint("ginecologia_obstetricia_id", "slug", name="uq_leccion_parent_slug"),
    )


class Video(Base):
    __tablename__ = "content_videos"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    url: Mapped[str] = mapped_column(String(512), nullable=False)
    especialidad: Mapped[str] = mapped_column(String(255), nullable=False)
    tema: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)

