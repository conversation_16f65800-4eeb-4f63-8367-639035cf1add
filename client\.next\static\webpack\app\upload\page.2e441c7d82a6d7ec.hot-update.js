"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/page.tsx":
/*!*****************************!*\
  !*** ./app/upload/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UploadPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ESPECIALIDADES = [\n    {\n        value: 'ginecologia-obstetricia',\n        label: 'Ginecología y Obstetricia'\n    },\n    {\n        value: 'medicina-interna',\n        label: 'Medicina Interna'\n    },\n    {\n        value: 'cirugia',\n        label: 'Cirugía'\n    },\n    {\n        value: 'pediatria',\n        label: 'Pediatría'\n    },\n    {\n        value: 'salud-publica',\n        label: 'Salud Pública'\n    }\n];\nconst TEMAS_GINECOBS = [\n    {\n        value: '1-control-prenatal',\n        label: 'Control Prenatal'\n    },\n    {\n        value: '2-embarazo-multiple',\n        label: 'Embarazo Múltiple'\n    },\n    {\n        value: '3-hemorragias-primera-mitad',\n        label: 'Hemorragias de la Primera Mitad del Embarazo'\n    },\n    {\n        value: '4-hemorragias-segunda-mitad',\n        label: 'Hemorragias de la Segunda Mitad del Embarazo'\n    },\n    {\n        value: '5-rotura-prematura-membranas',\n        label: 'Síndrome de Rotura Prematura de Membranas'\n    },\n    {\n        value: '6-parto-prematuro',\n        label: 'Parto Prematuro'\n    },\n    {\n        value: '7-embarazo-prolongado',\n        label: 'Embarazo Prolongado'\n    },\n    {\n        value: '8-hipertension-embarazo',\n        label: 'Estados Hipertensivos del Embarazo'\n    },\n    {\n        value: '9-diabetes-gestacional',\n        label: 'Diabetes Gestacional'\n    },\n    {\n        value: '10-trabajo-parto',\n        label: 'Trabajo de Parto'\n    },\n    {\n        value: '11-mecanismo-parto',\n        label: 'Mecanismo del Parto en General'\n    },\n    {\n        value: '12-atencion-parto',\n        label: 'Atención del Parto'\n    },\n    {\n        value: '13-lactancia-materna',\n        label: 'Lactancia Materna'\n    },\n    {\n        value: '14-puerperio-normal',\n        label: 'Puerperio Normal'\n    },\n    {\n        value: '15-puerperio-patologico',\n        label: 'Puerperio Patológico'\n    },\n    {\n        value: '16-its',\n        label: 'Infecciones de Transmisión Sexual'\n    },\n    {\n        value: '17-patologia-endometrio',\n        label: 'Patología del Endometrio'\n    },\n    {\n        value: '18-amenorrea',\n        label: 'Amenorrea'\n    },\n    {\n        value: '19-patologia-cervicouterina',\n        label: 'Patología Cervicouterina'\n    },\n    {\n        value: '20-cancer-mama',\n        label: 'Cáncer de Mama'\n    },\n    {\n        value: '21-cancer-ovario',\n        label: 'Cáncer de Ovario'\n    },\n    {\n        value: '22-climaterio-menopausia',\n        label: 'Climaterio y Menopausia'\n    },\n    {\n        value: '23-endometriosis',\n        label: 'Endometriosis'\n    },\n    {\n        value: '24-epi',\n        label: 'Enfermedad Pélvica Inflamatoria'\n    },\n    {\n        value: '25-infecciones-vaginales',\n        label: 'Infecciones Vaginales'\n    },\n    {\n        value: '26-miomatosis-uterina',\n        label: 'Miomatosis Uterina'\n    },\n    {\n        value: '27-patologia-benigna-mama',\n        label: 'Patología Benigna de Mama'\n    },\n    {\n        value: '28-patologia-benigna-ovario',\n        label: 'Patología Benigna de Ovario'\n    },\n    {\n        value: '29-salud-sexual',\n        label: 'Salud Sexual y Reproductiva'\n    },\n    {\n        value: '30-aborto',\n        label: 'Aborto'\n    },\n    {\n        value: '31-metrorragias',\n        label: 'Metrorragias'\n    },\n    {\n        value: '32-sop',\n        label: 'Síndrome de Ovario Poliquístico'\n    }\n];\nfunction UploadPage() {\n    var _ESPECIALIDADES_find, _TEMAS_GINECOBS_find, _ESPECIALIDADES_find1;\n    _s();\n    const [especialidad, setEspecialidad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tema, setTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [video, setVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [videos, setVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideo, setSelectedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch videos when component mounts\n    const fetchVideos = async ()=>{\n        try {\n            const response = await fetch('/api/v1/upload-content/videos');\n            if (!response.ok) throw new Error('Error al cargar los videos');\n            const data = await response.json();\n            setVideos(data);\n        } catch (error) {\n            setError('Error al cargar los videos');\n        }\n    };\n    // Load videos on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadPage.useEffect\": ()=>{\n            fetchVideos();\n        }\n    }[\"UploadPage.useEffect\"], []);\n    const handleDelete = async (videoId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar este video?')) return;\n        try {\n            const response = await fetch(\"/api/v1/upload-content/videos/\".concat(videoId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar el video');\n            // Remove video from state\n            setVideos(videos.filter((v)=>v.id !== videoId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (error) {\n            setError('Error al eliminar el video');\n        }\n    };\n    const handleEdit = (video)=>{\n        setSelectedVideo(video);\n        setIsEditing(true);\n        setEspecialidad(video.especialidad);\n        setTema(video.tema);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!especialidad || !tema || !isEditing && !video) {\n            setError('Por favor complete todos los campos');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            const formData = new FormData();\n            if (video) formData.append('file', video);\n            formData.append('especialidad', especialidad);\n            formData.append('tema', tema);\n            formData.append('titulo', video ? video.name : selectedVideo.titulo);\n            const url = isEditing ? \"/api/v1/upload-content/videos/\".concat(selectedVideo.id) : '/api/v1/upload-content/upload-video';\n            const response = await fetch(url, {\n                method: isEditing ? 'PUT' : 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                fetchVideos(); // Refresh the video list\n                if (!isEditing) {\n                    setVideo(null);\n                    setTema('');\n                    setEspecialidad('');\n                }\n            } else {\n                throw new Error(result.message || \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n            }\n        } catch (err) {\n            console.error('Error:', err);\n            setError(err instanceof Error ? err.message : \"Error al \".concat(isEditing ? 'actualizar' : 'subir', \" el video\"));\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-4xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Gestionar Lecciones\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-8 md:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: isEditing ? 'Editar Lección' : 'Subir Nueva Lección'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: \"Especialidad M\\xe9dica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full justify-between\",\n                                                            children: [\n                                                                especialidad ? (_ESPECIALIDADES_find = ESPECIALIDADES.find((e)=>e.value === especialidad)) === null || _ESPECIALIDADES_find === void 0 ? void 0 : _ESPECIALIDADES_find.label : 'Seleccione una especialidad',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"▼\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                        className: \"w-[300px]\",\n                                                        children: ESPECIALIDADES.map((esp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onSelect: ()=>{\n                                                                    setEspecialidad(esp.value);\n                                                                    setTema(''); // Reset tema when especialidad changes\n                                                                },\n                                                                className: \"cursor-pointer p-2 hover:bg-gray-100\",\n                                                                children: esp.label\n                                                            }, esp.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 17\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 9\n                                    }, this),\n                                    especialidad === 'ginecologia-obstetricia' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: \"Tema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full justify-between\",\n                                                            children: [\n                                                                tema ? (_TEMAS_GINECOBS_find = TEMAS_GINECOBS.find((t)=>t.value === tema)) === null || _TEMAS_GINECOBS_find === void 0 ? void 0 : _TEMAS_GINECOBS_find.label : 'Seleccione un tema',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"▼\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                        className: \"w-[300px]\",\n                                                        children: TEMAS_GINECOBS.map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onSelect: ()=>setTema(t.value),\n                                                                className: \"cursor-pointer p-2 hover:bg-gray-100\",\n                                                                children: t.label\n                                                            }, t.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 19\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 11\n                                    }, this),\n                                    especialidad && ![\n                                        'ginecologia-obstetricia'\n                                    ].includes(especialidad) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"tema\",\n                                                children: \"Tema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"tema\",\n                                                placeholder: \"Ingrese el tema de \".concat((_ESPECIALIDADES_find1 = ESPECIALIDADES.find((e)=>e.value === especialidad)) === null || _ESPECIALIDADES_find1 === void 0 ? void 0 : _ESPECIALIDADES_find1.label),\n                                                value: tema,\n                                                onChange: (e)=>setTema(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"video\",\n                                                children: \"Video de la lecci\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"video\",\n                                                type: \"file\",\n                                                accept: \"video/*\",\n                                                onChange: (e)=>{\n                                                    var _e_target_files;\n                                                    return setVideo(((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 11\n                                            }, this),\n                                            video && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Archivo seleccionado: \",\n                                                    video.name,\n                                                    \" (\",\n                                                    (video.size / (1024 * 1024)).toFixed(2),\n                                                    \" MB)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 9\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-red-100 border border-red-400 text-red-700 rounded\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 11\n                                    }, this),\n                                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-green-100 border border-green-400 text-green-700 rounded\",\n                                        children: \"\\xa1Lecci\\xf3n subida exitosamente!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"submit\",\n                                            disabled: isUploading,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                            children: isUploading ? 'Subiendo...' : 'Subir Lección'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 11\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 9\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Lecciones Subidas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: videos.map((video)=>{\n                                    var _ESPECIALIDADES_find;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border p-4 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: video.titulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    (_ESPECIALIDADES_find = ESPECIALIDADES.find((e)=>e.value === video.especialidad)) === null || _ESPECIALIDADES_find === void 0 ? void 0 : _ESPECIALIDADES_find.label,\n                                                    \" - \",\n                                                    video.tema\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleEdit(video),\n                                                        children: \"Editar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"destructive\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleDelete(video.id),\n                                                        children: \"Eliminar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, video.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\page.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(UploadPage, \"v7+DHRNQ+pKYgpYtd7PsCDeYjK0=\");\n_c = UploadPage;\nvar _c;\n$RefreshReg$(_c, \"UploadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/page.tsx\n"));

/***/ })

});