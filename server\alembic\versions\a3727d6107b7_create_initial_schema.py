"""create_initial_schema

Revision ID: a3727d6107b7
Revises: 
Create Date: 2025-08-20 13:33:33.482132

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a3727d6107b7'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('content_videos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('url', sa.String(length=512), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=512), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_content_videos_id'), 'content_videos', ['id'], unique=False)
    op.create_table('lecciones',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_index(op.f('ix_lecciones_id'), 'lecciones', ['id'], unique=False)
    op.create_table('videos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(length=512), nullable=False),
    sa.Column('content_type', sa.String(length=128), nullable=False),
    sa.Column('size', sa.BigInteger(), nullable=True),
    sa.Column('gcs_path', sa.String(length=1024), nullable=False),
    sa.Column('public_url', sa.String(length=1024), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_videos_id'), 'videos', ['id'], unique=False)
    op.create_table('ginecologia_obstetricia',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('lecciones_id', sa.Integer(), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['lecciones_id'], ['lecciones.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('lecciones_id', 'slug', name='uq_ginobs_lecciones_slug')
    )
    op.create_index(op.f('ix_ginecologia_obstetricia_id'), 'ginecologia_obstetricia', ['id'], unique=False)
    op.create_table('leccion',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ginecologia_obstetricia_id', sa.Integer(), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('video_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['ginecologia_obstetricia_id'], ['ginecologia_obstetricia.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['video_id'], ['content_videos.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ginecologia_obstetricia_id', 'slug', name='uq_leccion_parent_slug')
    )
    op.create_index(op.f('ix_leccion_id'), 'leccion', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_leccion_id'), table_name='leccion')
    op.drop_table('leccion')
    op.drop_index(op.f('ix_ginecologia_obstetricia_id'), table_name='ginecologia_obstetricia')
    op.drop_table('ginecologia_obstetricia')
    op.drop_index(op.f('ix_videos_id'), table_name='videos')
    op.drop_table('videos')
    op.drop_index(op.f('ix_lecciones_id'), table_name='lecciones')
    op.drop_table('lecciones')
    op.drop_index(op.f('ix_content_videos_id'), table_name='content_videos')
    op.drop_table('content_videos')
    # ### end Alembic commands ###
