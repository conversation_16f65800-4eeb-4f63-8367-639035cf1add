{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "96dpS1azgl9CScCtIzPvTVZYmNUzQ94jpaW1OC3gyj4=", "__NEXT_PREVIEW_MODE_ID": "529c7b706dc2e6f292e02c0d89d29799", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1e79a52c36d308fcce8daed3bfd5abc4b17253b0743b625a2e7c15540068d5e1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "42578a96b5e44601c40ff87d744929e204a7409ed16be3ccf466c2ed7a919867"}}}, "functions": {}, "sortedMiddleware": ["/"]}