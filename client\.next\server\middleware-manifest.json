{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "owHhThRxjPJpCN5reFk4ZLPLqgd9XWkWZgCHNX6jnH8=", "__NEXT_PREVIEW_MODE_ID": "1e91ea4527d9f263ea9199930a942c60", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "98ae326ce1f656fb4e6018edc9fe6a20a1954371b8c4a922b087ebad4149aba1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c6af5e3d2e308a10071bf94cd5423552cf09c67fec7ce78515dd979fa468ab17"}}}, "functions": {}, "sortedMiddleware": ["/"]}