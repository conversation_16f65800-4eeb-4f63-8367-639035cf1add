"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type Especialidad = 'ginecologia-obstetricia' | 'medicina-interna' | 'cirugia' | 'pediatria' | 'salud-publica';

const ESPECIALIDADES: { value: Especialidad; label: string }[] = [
  { value: 'ginecologia-obstetricia', label: 'Ginecología y Obstetricia' },
  { value: 'medicina-interna', label: 'Medicina Interna' },
  { value: 'cirugia', label: 'Cirugía' },
  { value: 'pediatria', label: 'Pediatría' },
  { value: 'salud-publica', label: 'Salud Pública' },
];

const TEMAS_GINECOBS: { value: string; label: string }[] = [
  { value: '1-control-prenatal', label: 'Control Prenatal' },
  { value: '2-embarazo-multiple', label: 'Embarazo Múltiple' },
  { value: '3-hemorragias-primera-mitad', label: 'Hemorragias de la Primera Mitad del Embarazo' },
  { value: '4-hemorragias-segunda-mitad', label: 'Hemorragias de la Segunda Mitad del Embarazo' },
  { value: '5-rotura-prematura-membranas', label: 'Síndrome de Rotura Prematura de Membranas' },
  { value: '6-parto-prematuro', label: 'Parto Prematuro' },
  { value: '7-embarazo-prolongado', label: 'Embarazo Prolongado' },
  { value: '8-hipertension-embarazo', label: 'Estados Hipertensivos del Embarazo' },
  { value: '9-diabetes-gestacional', label: 'Diabetes Gestacional' },
  { value: '10-trabajo-parto', label: 'Trabajo de Parto' },
  { value: '11-mecanismo-parto', label: 'Mecanismo del Parto en General' },
  { value: '12-atencion-parto', label: 'Atención del Parto' },
  { value: '13-lactancia-materna', label: 'Lactancia Materna' },
  { value: '14-puerperio-normal', label: 'Puerperio Normal' },
  { value: '15-puerperio-patologico', label: 'Puerperio Patológico' },
  { value: '16-its', label: 'Infecciones de Transmisión Sexual' },
  { value: '17-patologia-endometrio', label: 'Patología del Endometrio' },
  { value: '18-amenorrea', label: 'Amenorrea' },
  { value: '19-patologia-cervicouterina', label: 'Patología Cervicouterina' },
  { value: '20-cancer-mama', label: 'Cáncer de Mama' },
  { value: '21-cancer-ovario', label: 'Cáncer de Ovario' },
  { value: '22-climaterio-menopausia', label: 'Climaterio y Menopausia' },
  { value: '23-endometriosis', label: 'Endometriosis' },
  { value: '24-epi', label: 'Enfermedad Pélvica Inflamatoria' },
  { value: '25-infecciones-vaginales', label: 'Infecciones Vaginales' },
  { value: '26-miomatosis-uterina', label: 'Miomatosis Uterina' },
  { value: '27-patologia-benigna-mama', label: 'Patología Benigna de Mama' },
  { value: '28-patologia-benigna-ovario', label: 'Patología Benigna de Ovario' },
  { value: '29-salud-sexual', label: 'Salud Sexual y Reproductiva' },
  { value: '30-aborto', label: 'Aborto' },
  { value: '31-metrorragias', label: 'Metrorragias' },
  { value: '32-sop', label: 'Síndrome de Ovario Poliquístico' },
];

interface Video {
  id: number;
  title: string;
  especialidad: string;
  tema: string;
  url: string;
}

// Helper function to get API base URL
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

export default function UploadPage() {
  const [especialidad, setEspecialidad] = useState<Especialidad | ''>('');
  const [tema, setTema] = useState('');
  const [video, setVideo] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [videos, setVideos] = useState<Video[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Fetch videos when component mounts
  const fetchVideos = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/videos`);
      if (!response.ok) throw new Error('Error al cargar los videos');
      const data = await response.json();
      setVideos(data);
    } catch (err) {
      console.error('Error fetching videos:', err);
      setError('Error al cargar los videos');
    }
  };

  // Load videos on mount
  useEffect(() => {
    fetchVideos();
  }, []);

  const handleDelete = async (videoId: number) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este video?')) return;

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/videos/${videoId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar el video');

      // Remove video from state
      setVideos(videos.filter(v => v.id !== videoId));
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error deleting video:', err);
      setError('Error al eliminar el video');
    }
  };

  const handleEdit = (video: Video) => {
    setSelectedVideo(video);
    setIsEditing(true);
    setEspecialidad(video.especialidad as Especialidad);
    setTema(video.tema);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!especialidad || !tema || (!isEditing && !video)) {
      setError('Por favor complete todos los campos');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const formData = new FormData();
      if (video) formData.append('file', video);
      formData.append('especialidad', especialidad);
      formData.append('tema', tema);
      formData.append('titulo', video ? video.name : selectedVideo!.title);

      const url = isEditing
        ? `${getApiBaseUrl()}/api/v1/upload-content/videos/${selectedVideo!.id}`
        : `${getApiBaseUrl()}/api/v1/upload-content/upload-video`;

      const response = await fetch(url, {
        method: isEditing ? 'PUT' : 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `Error al ${isEditing ? 'actualizar' : 'subir'} el video`);
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess(true);
        fetchVideos(); // Refresh the video list
        if (!isEditing) {
          setVideo(null);
          setTema('');
          setEspecialidad('');
        }
      } else {
        throw new Error(result.message || `Error al ${isEditing ? 'actualizar' : 'subir'} el video`);
      }
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : `Error al ${isEditing ? 'actualizar' : 'subir'} el video`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Gestionar Lecciones</h1>
      
      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {/* Upload Form */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">
            {isEditing ? 'Editar Lección' : 'Subir Nueva Lección'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label>Especialidad Médica</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                {especialidad ? ESPECIALIDADES.find(e => e.value === especialidad)?.label : 'Seleccione una especialidad'}
                <span>▼</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[300px]">
              {ESPECIALIDADES.map((esp) => (
                <DropdownMenuItem 
                  key={esp.value} 
                  onSelect={() => {
                    setEspecialidad(esp.value);
                    setTema(''); // Reset tema when especialidad changes
                  }}
                  className="cursor-pointer p-2 hover:bg-gray-100"
                >
                  {esp.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {especialidad === 'ginecologia-obstetricia' && (
          <div className="space-y-2">
            <Label>Tema</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  {tema ? TEMAS_GINECOBS.find(t => t.value === tema)?.label : 'Seleccione un tema'}
                  <span>▼</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[300px]">
                {TEMAS_GINECOBS.map((t) => (
                  <DropdownMenuItem 
                    key={t.value}
                    onSelect={() => setTema(t.value)}
                    className="cursor-pointer p-2 hover:bg-gray-100"
                  >
                    {t.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {especialidad && (!['ginecologia-obstetricia'].includes(especialidad)) && (
          <div className="space-y-2">
            <Label htmlFor="tema">Tema</Label>
            <Input 
              id="tema" 
              placeholder={`Ingrese el tema de ${ESPECIALIDADES.find(e => e.value === especialidad)?.label}`}
              value={tema}
              onChange={(e) => setTema(e.target.value)}
              required
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="video">Video de la lección</Label>
          <Input 
            id="video" 
            type="file" 
            accept="video/*" 
            onChange={(e) => setVideo(e.target.files?.[0] || null)}
            required
          />
          {video && (
            <p className="text-sm text-muted-foreground">
              Archivo seleccionado: {video.name} ({(video.size / (1024 * 1024)).toFixed(2)} MB)
            </p>
          )}
        </div>

        {error && (
          <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            ¡Lección subida exitosamente!
          </div>
        )}

        <div className="flex justify-end">
          <Button 
            type="submit" 
            disabled={isUploading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isUploading ? 'Subiendo...' : 'Subir Lección'}
          </Button>
        </div>
      </form>
    </div>

        {/* Video List */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Lecciones Subidas</h2>
          <div className="space-y-4">
            {videos.map((video) => (
              <div key={video.id} className="border p-4 rounded">
                <h3 className="font-semibold">{video.title}</h3>
                <p className="text-sm text-gray-600">
                  {ESPECIALIDADES.find(e => e.value === video.especialidad)?.label} - {video.tema}
                </p>
                <div className="mt-2 space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(video)}
                  >
                    Editar
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(video.id)}
                  >
                    Eliminar
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
