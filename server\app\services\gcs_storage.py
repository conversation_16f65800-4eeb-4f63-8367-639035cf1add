from __future__ import annotations

import os
from datetime import datetime
from typing import Optional

from fastapi import UploadFile, HTTPException
from google.cloud import storage
from google.oauth2 import service_account
from ..core.config import get_settings

settings = get_settings()

class GCSStorage:
    _instance = None
    _client = None
    _bucket = None
    _bucket_name = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GCSStorage, cls).__new__(cls)
            cls._initialize()
        return cls._instance

    @classmethod
    def _initialize(cls):
        """Initialize the GCS client and bucket."""
        print("[GCS] Initializing Google Cloud Storage client...")
        try:
            # Get GCS credentials from environment
            print("[GCS] Loading credentials from settings...")
            credentials_info = {
                "type": "service_account",
                "project_id": settings.GOOGLE_CLOUD_PROJECT_ID,
                "private_key_id": settings.GOOGLE_CLOUD_PRIVATE_KEY_ID,
                "private_key": settings.GOOGLE_CLOUD_PRIVATE_KEY.replace('\\n', '\n'),
                "client_email": settings.GOOGLE_CLOUD_CLIENT_EMAIL,
                "client_id": settings.GOOGLE_CLOUD_CLIENT_ID,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_x509_cert_url": settings.GOOGLE_CLOUD_CLIENT_CERT_URL
            }
            
            print("[GCS] Creating credentials...")
            credentials = service_account.Credentials.from_service_account_info(credentials_info)
            
            print(f"[GCS] Initializing client for project: {settings.GOOGLE_CLOUD_PROJECT_ID}")
            cls._client = storage.Client(credentials=credentials, project=settings.GOOGLE_CLOUD_PROJECT_ID)
            
            print(f"[GCS] Setting up bucket: {settings.GOOGLE_CLOUD_STORAGE_BUCKET}")
            cls._bucket_name = settings.GOOGLE_CLOUD_STORAGE_BUCKET
            cls._bucket = cls._client.bucket(cls._bucket_name)
            
            # Ensure the bucket exists
            print("[GCS] Checking if bucket exists...")
            if not cls._bucket.exists():
                print(f"[GCS] Creating bucket: {cls._bucket_name}")
                cls._bucket = cls._client.create_bucket(cls._bucket_name)
            print("[GCS] Bucket ready")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize GCS client: {str(e)}")

    async def upload_file(
        self,
        file: UploadFile,
        destination_path: str,
        content_type: Optional[str] = None
    ) -> str:
        """
        Upload a file to Google Cloud Storage.
        
        Args:
            file: The file to upload
            destination_path: The path where the file will be stored in the bucket
            content_type: Optional MIME type of the file
            
        Returns:
            str: Public URL of the uploaded file
        """
        try:
            # Ensure the client and bucket are initialized
            if self._client is None or self._bucket is None:
                self._initialize()

            # Create a new blob and upload the file
            blob = self._bucket.blob(destination_path)
            
            # Set content type if provided
            if content_type:
                blob.content_type = content_type
            
            # Upload the file
            file_content = await file.read()
            blob.upload_from_string(
                file_content,
                content_type=content_type or file.content_type
            )
            
            # Make the blob publicly accessible
            blob.make_public()
            
            return blob.public_url
            
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error uploading file to Google Cloud Storage: {str(e)}"
            )

    async def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from Google Cloud Storage.
        
        Args:
            file_path: The path of the file to delete in the bucket
            
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            # Ensure the client and bucket are initialized
            if self._client is None or self._bucket is None:
                self._initialize()

            # Get the blob and delete it
            blob = self._bucket.blob(file_path)
            if not blob.exists():
                return False
                
            blob.delete()
            return True
            
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting file from Google Cloud Storage: {str(e)}"
            )

    def generate_signed_url(
        self,
        object_name: str,
        content_type: str,
        expires_in_seconds: int = 300,
        method: str = 'PUT'
    ) -> str:
        """
        Generate a signed URL for uploading a file to Google Cloud Storage.
        
        Args:
            object_name: The name/path of the object in the bucket
            content_type: The content type of the file
            expires_in_seconds: How long the signed URL should be valid for
            method: The HTTP method to allow (default is PUT for upload)
            
        Returns:
            str: The signed URL for uploading
        """
        try:
            # Ensure the client and bucket are initialized
            if self._client is None or self._bucket is None:
                self._initialize()

            # Create a new blob
            blob = self._bucket.blob(object_name)
            
            # Generate signed URL
            url = blob.generate_signed_url(
                version='v4',
                expiration=datetime.timedelta(seconds=expires_in_seconds),
                method=method,
                content_type=content_type,
            )
            
            return url
            
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error generating signed URL: {str(e)}"
            )

    def get_public_url(self, object_name: str) -> str:
        """
        Get the public URL for a file in Google Cloud Storage.
        
        Args:
            object_name: The name/path of the object in the bucket
            
        Returns:
            str: The public URL of the file
        """
        try:
            # Ensure the client and bucket are initialized
            if self._client is None or self._bucket is None:
                self._initialize()

            # Create a new blob
            blob = self._bucket.blob(object_name)
            return f"https://storage.googleapis.com/{self._bucket_name}/{object_name}"
            
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error getting public URL: {str(e)}"
            )


# Helper functions to use the GCSStorage class
_storage = GCSStorage()

async def upload_file_to_gcs(file: UploadFile, file_path: str, content_type: Optional[str] = None) -> str:
    return await _storage.upload_file(file, file_path, content_type)

async def delete_file_from_gcs(file_path: str) -> bool:
    return await _storage.delete_file(file_path)

# Create a singleton instance
gcs_storage = GCSStorage()

async def upload_file_to_gcs(
    file: UploadFile,
    file_path: str,
    content_type: str = "application/octet-stream"
) -> str:
    """
    Helper function to upload a file to Google Cloud Storage.
    
    Args:
        file: The file to upload
        file_path: The path where the file will be stored in the bucket
        content_type: Optional MIME type of the file
        
    Returns:
        str: Public URL of the uploaded file
    """
    return await gcs_storage.upload_file(file, file_path, content_type)

def generate_signed_upload_url(
    bucket_name: str,
    object_name: str,
    content_type: str,
    expires_in_seconds: int = 300
) -> str:
    """Helper function to generate a signed upload URL."""
    return gcs_storage.generate_signed_url(object_name, content_type, expires_in_seconds)

def build_public_url(bucket_name: str, object_name: str) -> str:
    """Helper function to build a public URL for an object."""
    return gcs_storage.get_public_url(object_name)
